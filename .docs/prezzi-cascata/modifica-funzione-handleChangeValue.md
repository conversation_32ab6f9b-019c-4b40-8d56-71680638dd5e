# Analisi e Modifica della funzione handleChangeValue

## Analisi della funzione saveValue attuale

La funzione `saveValue` attuale funziona solo per proprietà a livello di sigla:

```javascript
async saveValue(key, sigla, param, newValue) {
    const day = this.calendario[key];
    if (day) {
        const siglaData = day.sigle.find((s) => s.siglaUa === sigla);
        if (siglaData) {
            siglaData[param] = newValue * 1; // Salva nella sigla specifica
        }
    }
}
```

**Problema**: Non può salvare proprietà a livello di data (come `tipoTariffa` e `modificaPerc`).

## Problemi identificati in handleChangeValue

1. **Case `modificaperccol`**: Completamente vuoto, manca implementazione
2. **Case `tipotariffa`**: Non salva il valore nel calendario, solo chiama `handleComboChange`
3. **Mancanza di funzione per salvare a livello data**: Serve una nuova funzione `saveValueToDate`

## Implementazione richiesta

### 1. Nuova funzione saveValueToDate

```javascript
/**
 * Salva un valore a livello di data (non sigla specifica)
 * @param {string|number} key - Chiave della riga del calendario
 * @param {string} param - Parametro da salvare
 * @param {*} value - Valore da salvare
 */
async saveValueToDate(key, param, value) {
    console.log('[useSimulatore] saveValueToDate');
    const day = this.calendario[key];
    if (day) {
        day[param] = value;
        await this.saveCalendarioToIndexedDB(toRaw(this.calendario));
        if (this.isDebugMode) {
            console.log("Salvato valore", value, "per parametro", param, "data", key);
        }
    } else {
        console.error(`Chiave ${key} mancante nel calendario`);
    }
}
```

### 2. Funzione handleChangeValue reingegnerizzata

```javascript
/**
 * Gestisce il cambiamento di valore per una cella della tabella (store).
 * @param {string|number} key - Chiave della riga del calendario.
 * @param {string|null} sigla - Sigla identificativa della colonna.
 * @param {string} param - Parametro modificato.
 * @param {*} value - Nuovo valore da assegnare.
 */
handleChangeValue(key, sigla, param, value) {
    console.log('[useSimulatore] handleChangeValue', { key, sigla, param, value });

    switch (param) {
        case "tipotariffa":
            // Salva il tipo tariffa a livello di data
            this.saveValueToDate && this.saveValueToDate(key, 'tipoTariffa', value);
            // Applica il cambio a tutte le sigle della data
            setTimeout(() => { this.handleComboChange(key, value); }, 50);
            break;

        case "modificaperccol":
            // Salva la modifica percentuale a livello di data (colonna)
            this.saveValueToDate && this.saveValueToDate(key, 'modificaPerc', value);
            break;

        case "garantito":
            // Salva a livello di sigla specifica
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;

        case "modificaperc":
            // Salva a livello di sigla specifica
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;

        case "prezzoforzato":
            // Salva a livello di sigla specifica
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;

        default:
            console.warn(`Parametro ${param} non gestito in handleChangeValue`);
            break;
    }

    // Ricalcola i prezzi dopo ogni modifica
    setTimeout(() => {
        this.saveCalendarioToIndexedDB && this.saveCalendarioToIndexedDB(this.calendario);

        // Per modifiche a livello di colonna, ricalcola tutte le sigle
        if (param === "tipotariffa" || param === "modificaperccol") {
            const day = this.calendario[key];
            if (day && day.sigle) {
                day.sigle.forEach(siglaData => {
                    this.changePrezzoByInput(key, siglaData.siglaUa, param, value);
                });
            }
        } else {
            // Per modifiche a livello di sigla, ricalcola solo quella sigla
            this.changePrezzoByInput(key, sigla, param, value);
        }
    }, 50);
}
```

### 3. Aggiornamento handleComboChange

```javascript
/**
 * Gestisce il cambio del valore selezionato in una combo (select) (store).
 * @param {string|number} key - Chiave della riga del calendario.
 * @param {string} value - Valore selezionato nella combo.
 */
handleComboChange(key, value) {
    console.log('[useSimulatore] handleComboChange');
    const calendarRow = this.calendario[key];
    if (!calendarRow) return;

    // Aggiorna il tipoTariffa a livello di data
    calendarRow.tipoTariffa = value;

    const calendarioRowSigle = calendarRow.sigle;
    const valueMap = {
        C: "costo",
        M: "prezzomup",
        R: "prezzorevenue",
        A: "concorrenza",
        B: "rmc",
    };
    const newValueKey = valueMap[value] || "";

    // Applica il nuovo prezzo base a tutte le sigle
    calendarioRowSigle.forEach((sigla) => {
        sigla.prezzo = sigla[newValueKey] || "";
    });
}
```

## Differenze tra modifiche a livello colonna vs sigla

- **Livello colonna** (`tipotariffa`, `modificaperccol`):
  - Salvate nel `calendario[key]` (a livello data)
  - Influenzano TUTTE le sigle di quella data
  - Richiedono ricalcolo di tutte le sigle

- **Livello sigla** (`garantito`, `modificaperc`, `prezzoforzato`):
  - Salvate in `calendario[key].sigle[].siglaUa` (a livello sigla)
  - Influenzano solo la sigla specifica
  - Richiedono ricalcolo solo di quella sigla

## Implementazione nel file useSimulatore.js

Per implementare queste modifiche nel file `useSimulatore.js`, è necessario:

1. **Aggiungere la funzione `saveValueToDate`** dopo la funzione `saveValue` esistente
2. **Sostituire completamente la funzione `handleChangeValue`** con la versione reingegnerizzata
3. **Aggiornare la funzione `handleComboChange`** per salvare correttamente il `tipoTariffa`
4. **Aggiornare la funzione `changePrezzoByInput`** per implementare l'algoritmo a cascata come descritto nel file `spiegazione.md`

Queste modifiche garantiranno che:
- Le modifiche a livello di colonna vengano salvate correttamente a livello di data
- Le modifiche a livello di sigla vengano salvate correttamente a livello di sigla
- Tutti i prezzi vengano ricalcolati correttamente dopo ogni modifica
- L'algoritmo a cascata venga applicato per determinare il prezzo finale
