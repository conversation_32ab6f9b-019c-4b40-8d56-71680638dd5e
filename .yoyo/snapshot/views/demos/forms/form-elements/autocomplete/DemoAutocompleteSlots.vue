<script setup>
import avatar1 from '@images/avatars/avatar-1.png'
import avatar2 from '@images/avatars/avatar-2.png'
import avatar3 from '@images/avatars/avatar-3.png'
import avatar4 from '@images/avatars/avatar-4.png'
import avatar5 from '@images/avatars/avatar-5.png'
import avatar6 from '@images/avatars/avatar-6.png'
import avatar7 from '@images/avatars/avatar-7.png'
import avatar8 from '@images/avatars/avatar-8.png'

const friends = ref([
  '<PERSON>',
  '<PERSON><PERSON><PERSON>',
])

const people = [
  {
    name: '<PERSON>',
    group: 'Group 1',
    avatar: avatar1,
  },
  {
    name: '<PERSON>',
    group: 'Group 1',
    avatar: avatar2,
  },
  {
    name: '<PERSON>',
    group: 'Group 1',
    avatar: avatar3,
  },
  {
    name: '<PERSON>',
    group: 'Group 1',
    avatar: avatar4,
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    group: 'Group 2',
    avatar: avatar5,
  },
  {
    name: '<PERSON> ',
    group: 'Group 2',
    avatar: avatar6,
  },
  {
    name: '<PERSON>',
    group: 'Group 2',
    avatar: avatar7,
  },
  {
    name: '<PERSON> Williams',
    group: 'Group 2',
    avatar: avatar8,
  },
]
</script>

<template>
  <VAutocomplete
    v-model="friends"
    chips
    closable-chips
    multiple
    :items="people"
    item-title="name"
    item-value="name"
    placeholder="Select User"
    label="Select"
  >
    <template #chip="{ props, item }">
      <VChip
        v-bind="props"
        :prepend-avatar="item.raw.avatar"
        :text="item.raw.name"
      />
    </template>

    <template #item="{ props, item }">
      <VListItem
        v-bind="props"
        :prepend-avatar="item?.raw?.avatar"
        :title="item?.raw?.name"
        :subtitle="item?.raw?.group"
      />
    </template>
  </VAutocomplete>
</template>
