<script setup>
import { register } from 'swiper/element/bundle'
import swiper10 from '@images/banner/banner-10.jpg'
import swiper11 from '@images/banner/banner-11.jpg'
import swiper20 from '@images/banner/banner-20.jpg'
import swiper7 from '@images/banner/banner-7.jpg'
import swiper8 from '@images/banner/banner-8.jpg'
import swiper9 from '@images/banner/banner-9.jpg'

register()
</script>

<template>
  <ClientOnly>
    <swiper-container
      pagination="true"
      navigation="true"
      autoplay="true"
      centered-slides="true"
      events-prefix="swiper-"
    >
      <swiper-slide
        v-for="swiperImg in
          [
            swiper7,
            swiper8,
            swiper9,
            swiper10,
            swiper11,
            swiper20,
          ]"
        :key="swiperImg"
      >
        <VImg
          :src="swiperImg"
          cover
        />
      </swiper-slide>
    </swiper-container>
  </ClientOnly>
</template>
