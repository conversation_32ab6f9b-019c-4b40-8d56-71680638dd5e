<script setup>
import { register } from 'swiper/element/bundle'
import swiper31 from '@images/banner/banner-31.jpg'
import swiper32 from '@images/banner/banner-32.jpg'
import swiper33 from '@images/banner/banner-33.jpg'
import swiper34 from '@images/banner/banner-34.jpg'
import swiper35 from '@images/banner/banner-35.jpg'

register()
</script>

<template>
  <ClientOnly>
    <swiper-container
      pagination="true"
      slides-per-view="3"
      space-between="25"
      events-prefix="swiper-"
    >
      <swiper-slide
        v-for="swiperImg in [
          swiper31,
          swiper32,
          swiper33,
          swiper34,
          swiper35,
        ]"
        :key="swiperImg"
      >
        <VImg :src="swiperImg" />
      </swiper-slide>
    </swiper-container>
  </ClientOnly>
</template>
