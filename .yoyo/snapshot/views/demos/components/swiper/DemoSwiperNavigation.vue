<script setup>
import { register } from 'swiper/element/bundle'
import swiper14 from '@images/banner/banner-14.jpg'
import swiper2 from '@images/banner/banner-2.jpg'
import swiper3 from '@images/banner/banner-3.jpg'
import swiper4 from '@images/banner/banner-4.jpg'
import swiper7 from '@images/banner/banner-7.jpg'

register()
</script>

<template>
  <ClientOnly>
    <swiper-container
      navigation="true"
      events-prefix="swiper-"
    >
      <swiper-slide
        v-for="swiperImg in [
          swiper7,
          swiper4,
          swiper14,
          swiper3,
          swiper2,
        ]"
        :key="swiperImg"
      >
        <VImg :src="swiperImg" />
      </swiper-slide>
    </swiper-container>
  </ClientOnly>
</template>
