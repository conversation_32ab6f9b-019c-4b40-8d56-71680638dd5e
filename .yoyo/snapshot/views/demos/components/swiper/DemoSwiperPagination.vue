<script setup>
import { register } from 'swiper/element/bundle'
import swiper8 from '@images/banner/banner-8.jpg'
import swiper9 from '@images/banner/banner-9.jpg'
import swiper12 from '@images/banner/banner-12.jpg'
import swiper20 from '@images/banner/banner-20.jpg'
import swiper7 from '@images/banner/banner-7.jpg'

register()
</script>

<template>
  <ClientOnly>
    <swiper-container
      pagination="true"
      events-prefix="swiper-"
    >
      <swiper-slide
        v-for="swiperImg in [
          swiper12,
          swiper9,
          swiper8,
          swiper7,
          swiper20,
        ]"
        :key="swiperImg"
      >
        <VImg :src="swiperImg" />
      </swiper-slide>
    </swiper-container>
  </ClientOnly>
</template>
