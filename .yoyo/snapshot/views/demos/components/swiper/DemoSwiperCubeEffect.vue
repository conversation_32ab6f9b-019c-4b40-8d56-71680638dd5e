<script setup>
import { register } from 'swiper/element/bundle'
import swiper21 from '@images/banner/banner-21.jpg'
import swiper23 from '@images/banner/banner-23.jpg'
import swiper24 from '@images/banner/banner-24.jpg'
import swiper32 from '@images/banner/banner-32.jpg'

register()
</script>

<template>
  <ClientOnly>
    <swiper-container
      pagination="true"
      centered-slides="true"
      effect="cube"
      grab-cursor="true"
      cube-effect-shadow="true"
      cube-effect-slide-shadows="true"
      cube-effect-shadow-scale="0.94"
      events-prefix="swiper-"
    >
      <swiper-slide
        v-for="swiperImg in [
          swiper21,
          swiper32,
          swiper23,
          swiper24,
        ]"
        :key="swiperImg"
      >
        <VImg :src="swiperImg" />
      </swiper-slide>
    </swiper-container>
  </ClientOnly>
</template>

<style lang="scss" scoped>
swiper-slide {
  background-position: center;
  background-size: cover;
  block-size: 250px;
  inline-size: 250px;
}

swiper-container {
  margin: auto;
  block-size: 250px;
  inline-size: 250px;
}
</style>
