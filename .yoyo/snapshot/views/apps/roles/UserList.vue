<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTranslateUtil } from '@/utils/translate-util' // Assumi che il file sia in utils

const { t, tc } = useTranslateUtil()

const searchQuery = ref('')
const selectedRole = ref()
const selectedPlan = ref()
const selectedStatus = ref()

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

const updateOptions = options => {
  page.value = options.page
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// Headers
const headers = ref([])

const updateHeaders = () => {
  headers.value = [
    { title: t('User'), key: 'user' },
    { title: t('Email'), key: 'email' },
    { title: t('Role'), key: 'role' },
    { title: t('Plan'), key: 'plan' },
    { title: t('Status'), key: 'status' },
    { title: t('Actions'), key: 'actions', sortable: false },
  ]
}

// Aggiorna i titoli delle colonne inizialmente e ogni volta che cambia la lingua
updateHeaders()
watch(() => t('User'), updateHeaders)

const {
  data: usersData,
  execute: fetchUsers,
} = await useApi(createUrl('/apps/users', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    plan: selectedPlan,
    role: selectedRole,
    itemsPerPage,
    page,
    sortBy,
    orderBy,
  },
}))

const users = computed(() => usersData.value.users)
const totalUsers = computed(() => usersData.value.totalUsers)

// 👉 search filters
const roles = [
  { title: t('Admin'), value: 'admin' },
  { title: t('Author'), value: 'author' },
  { title: t('Editor'), value: 'editor' },
  { title: t('Maintainer'), value: 'maintainer' },
  { title: t('Subscriber'), value: 'subscriber' },
]

const resolveUserRoleVariant = role => {
  const roleLowerCase = role.toLowerCase()
  if (roleLowerCase === 'subscriber') {
    return { color: 'success', icon: 'ri-user-line' }
  }
  if (roleLowerCase === 'author') {
    return { color: 'error', icon: 'ri-computer-line' }
  }
  if (roleLowerCase === 'maintainer') {
    return { color: 'info', icon: 'ri-pie-chart-line' }
  }
  if (roleLowerCase === 'editor') {
    return { color: 'warning', icon: 'ri-edit-box-line' }
  }
  if (roleLowerCase === 'admin') {
    return { color: 'primary', icon: 'ri-vip-crown-line' }
  }

  return { color: 'success', icon: 'ri-user-line' }
}

const resolveUserStatusVariant = stat => {
  const statLowerCase = stat.toLowerCase()
  if (statLowerCase === 'pending') return 'warning'
  if (statLowerCase === 'active') return 'success'
  if (statLowerCase === 'inactive') return 'secondary'

  return 'primary'
}

const deleteUser = async id => {
  await $api(`/apps/users/${ id }`, { method: 'DELETE' })

  // refetch User
  fetchUsers()
}
</script>

<template>
  <section>
    <VCard>
      <VCardText class="d-flex flex-wrap gap-4">
        <!-- 👉 Export button -->
        <VBtn
          variant="outlined"
          color="secondary"
          prepend-icon="ri-share-box-line"
        >
          {{ t('Export') }}
        </VBtn>

        <VSpacer />

        <div class="app-user-search-filter d-flex flex-wrap gap-4">
          <!-- 👉 Search  -->
          <div style="inline-size: 15.625rem;">
            <VTextField
              v-model="searchQuery"
              :placeholder="t('Search User')"
              density="compact"
            />
          </div>

          <!-- 👉 Add user button -->
          <div style="inline-size: 10rem;">
            <VSelect
              v-model="selectedRole"
              :placeholder="t('Select Role')"
              :items="roles"
              density="compact"
              clearable
              clear-icon="ri-close-line"
            />
          </div>
        </div>
      </VCardText>

      <!-- SECTION datatable -->
      <VDataTableServer
        v-model:items-per-page="itemsPerPage"
        :items-per-page-options="[
          { value: 10, title: '10' },
          { value: 20, title: '20' },
          { value: 50, title: '50' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        :items="users"
        item-value="id"
        :items-length="totalUsers"
        :headers="headers"
        show-select
        class="text-no-wrap rounded-0"
        @update:options="updateOptions"
      >
        <!-- User -->
        <template #item.user="{ item }">
          <div class="d-flex">
            <VAvatar
              size="34"
              :variant="!item.avatar ? 'tonal' : undefined"
              :color="!item.avatar ? resolveUserRoleVariant(item.role).color : undefined"
              class="me-3"
            >
              <VImg
                v-if="item.avatar"
                :src="item.avatar"
              />
              <span v-else>{{ avatarText(item.fullName) }}</span>
            </VAvatar>
            <div class="d-flex flex-column">
              <NuxtLink
                :to="{ name: 'apps-user-view-id', params: { id: item.id } }"
                class="text-h6"
              >
                {{ item.fullName }}
              </NuxtLink>
              <span class="text-sm">@{{ item.username }}</span>
            </div>
          </div>
        </template>

        <!-- Role -->
        <template #item.role="{ item }">
          <div class="d-flex gap-4">
            <VIcon
              size="22"
              :icon="resolveUserRoleVariant(item.role).icon"
              :color="resolveUserRoleVariant(item.role).color"
            />
            <h6 class="text-h6 text-capitalize font-weight-regular">
              {{ tc(item.role) }}
            </h6>
          </div>
        </template>

        <!-- Plan -->
        <template #item.plan="{ item }">
          <h6 class="text-h6 font-weight-regular text-capitalize">
            {{ tc(item.currentPlan) }}
          </h6>
        </template>

        <!-- Status -->
        <template #item.status="{ item }">
          <VChip
            :color="resolveUserStatusVariant(item.status)"
            size="small"
            class="text-capitalize"
          >
            {{ tc(item.status) }}
          </VChip>
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn
            size="small"
            @click="deleteUser(item.id)"
          >
            <VIcon icon="ri-delete-bin-7-line" />
          </IconBtn>

          <IconBtn
            size="small"
            :to="{ name: 'apps-user-view-id', params: { id: item.id } }"
          >
            <VIcon icon="ri-eye-line" />
          </IconBtn>

          <IconBtn size="small">
            <VIcon icon="ri-more-2-line" />

            <VMenu activator="parent">
              <VList>
                <VListItem link>
                  <template #prepend>
                    <VIcon
                      size="20"
                      icon="ri-edit-box-line"
                    />
                  </template>
                  <VListItemTitle>{{ t('Edit') }}</VListItemTitle>
                </VListItem>
                <VListItem link>
                  <template #prepend>
                    <VIcon
                      size="20"
                      icon="ri-download-line"
                    />
                  </template>
                  <VListItemTitle>{{ t('Download') }}</VListItemTitle>
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <!-- Pagination -->
        <template #bottom>
          <VDivider />

          <div class="d-flex justify-end flex-wrap gap-x-6 px-2 py-1">
            <div class="d-flex align-center gap-x-2 text-medium-emphasis text-base">
              {{ t('Rows Per Page:') }}
              <VSelect
                v-model="itemsPerPage"
                class="per-page-select"
                variant="plain"
                :items="[10, 20, 25, 50, 100]"
              />
            </div>

            <p class="d-flex align-center text-base text-high-emphasis me-2 mb-0">
              {{ paginationMeta({ page, itemsPerPage }, totalUsers) }}
            </p>

            <div class="d-flex gap-x-2 align-center me-2">
              <VBtn
                class="flip-in-rtl"
                icon="ri-arrow-left-s-line"
                variant="text"
                density="comfortable"
                color="high-emphasis"
                :disabled="page <= 1"
                @click="page <= 1 ? page = 1 : page--"
              />

              <VBtn
                class="flip-in-rtl"
                icon="ri-arrow-right-s-line"
                density="comfortable"
                variant="text"
                color="high-emphasis"
                :disabled="page >= Math.ceil(totalUsers / itemsPerPage)"
                @click="page >= Math.ceil(totalUsers / itemsPerPage) ? page = Math.ceil(totalUsers / itemsPerPage) : page++ "
              />
            </div>
          </div>
        </template>
      </VDataTableServer>
      <!-- SECTION -->
    </VCard>
  </section>
</template>

<style lang="scss">
.text-capitalize {
  text-transform: capitalize;
}
</style>
