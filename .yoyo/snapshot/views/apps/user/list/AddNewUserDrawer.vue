<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { ref, nextTick } from 'vue'
import {  useTranslateUtil } from '@/utils/translate-util'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'userData',
])

const { t } = useI18n()
const tc = t
const isFormValid = ref(false)
const refForm = ref()
const fullName = ref('')
const userName = ref('')
const email = ref('')
const company = ref('')
const country = ref()
const contact = ref('')
const role = ref()
const plan = ref()
const status = ref()

// 👉 drawer close
const closeNavigationDrawer = () => {
  emit('update:isDrawerOpen', false)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = () => {
  refForm.value?.validate().then(({ valid }) => {
    if (valid) {
      emit('userData', {
        id: 0,
        fullName: fullName.value,
        company: company.value,
        role: role.value,
        username: userName.value,
        country: country.value,
        contact: contact.value,
        email: email.value,
        currentPlan: plan.value,
        status: status.value,
        avatar: '',
      })
      emit('update:isDrawerOpen', false)
      nextTick(() => {
        refForm.value?.reset()
        refForm.value?.resetValidation()
      })
    }
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    temporary
    :width="400"
    location="end"
    class="scrollable-content"
    :model-value="props.isDrawerOpen"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Title -->
    <AppDrawerHeaderSection
      :title="tc('Add User')"
      @cancel="closeNavigationDrawer"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Form -->
          <VForm
            ref="refForm"
            v-model="isFormValid"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <!-- 👉 Full name -->
              <VCol cols="12">
                <VTextField
                  v-model="fullName"
                  :rules="[requiredValidator]"
                  :label="tc('Full Name')"
                  :placeholder="tc('John Doe')"
                />
              </VCol>

              <!-- 👉 Username -->
              <VCol cols="12">
                <VTextField
                  v-model="userName"
                  :rules="[requiredValidator]"
                  :label="tc('Username')"
                  :placeholder="tc('Johndoe')"
                />
              </VCol>

              <!-- 👉 Email -->
              <VCol cols="12">
                <VTextField
                  v-model="email"
                  :rules="[requiredValidator, emailValidator]"
                  :label="tc('Email')"
                  :placeholder="tc('<EMAIL>')"
                />
              </VCol>

              <!-- 👉 company -->
              <VCol cols="12">
                <VTextField
                  v-model="company"
                  :rules="[requiredValidator]"
                  :label="tc('Company')"
                  :placeholder="tc('Themeselection')"
                />
              </VCol>

              <!-- 👉 Country -->
              <VCol cols="12">
                <VSelect
                  v-model="country"
                  :label="tc('Select Country')"
                  :placeholder="tc('Select Country')"
                  :rules="[requiredValidator]"
                  :items="[
                    tc('USA'),
                    tc('UK'),
                    tc('India'),
                    tc('Australia')
                  ]"
                />
              </VCol>

              <!-- 👉 Contact -->
              <VCol cols="12">
                <VTextField
                  v-model="contact"
                  type="number"
                  :rules="[requiredValidator]"
                  :label="tc('Contact')"
                  :placeholder="tc('+1-541-754-3010')"
                />
              </VCol>

              <!-- 👉 Role -->
              <VCol cols="12">
                <VSelect
                  v-model="role"
                  :label="tc('Select Role')"
                  :placeholder="tc('Select Role')"
                  :rules="[requiredValidator]"
                  :items="[
                    tc('Admin'),
                    tc('Author'),
                    tc('Editor'),
                    tc('Maintainer'),
                    tc('Subscriber')
                  ]"
                />
              </VCol>

              <!-- 👉 Plan -->
              <VCol cols="12">
                <VSelect
                  v-model="plan"
                  :label="tc('Select Plan')"
                  :placeholder="tc('Select Plan')"
                  :rules="[requiredValidator]"
                  :items="[
                    tc('Basic'),
                    tc('Company'),
                    tc('Enterprise'),
                    tc('Team')
                  ]"
                />
              </VCol>

              <!-- 👉 Status -->
              <VCol cols="12">
                <VSelect
                  v-model="status"
                  :label="tc('Select Status')"
                  :placeholder="tc('Select Status')"
                  :rules="[requiredValidator]"
                  :items="[
                    { title: tc('Active'), value: 'active' },
                    { title: tc('Inactive'), value: 'inactive' },
                    { title: tc('Pending'), value: 'pending' }
                  ]"
                />
              </VCol>

              <!-- 👉 Submit and Cancel -->
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-4"
                >
                  {{ tc('Submit') }}
                </VBtn>
                <VBtn
                  type="reset"
                  variant="outlined"
                  color="error"
                  @click="closeNavigationDrawer"
                >
                  {{ tc('Cancel') }}
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
