<script setup>
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { ref, nextTick } from 'vue'
import { useTranslateUtil } from '@/utils/translate-util'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'structureData',
])

const { t, tc } = useTranslateUtil()

const isFormValid = ref(false)
const refForm = ref()
const id = ref('')
const businessName = ref('')
const location = ref()
const vatNumber = ref('')
const address = ref('')
const city = ref('')
const zipCode = ref('')
const province = ref('')
const ownerName = ref('')

// 👉 drawer close
const closeNavigationDrawer = () => {
  emit('update:isDrawerOpen', false)
  nextTick(() => {
    refForm.value?.reset()
    refForm.value?.resetValidation()
  })
}

const onSubmit = () => {
  refForm.value?.validate().then(({ valid }) => {
    if (valid) {
      emit('structureData', {
        id: id.value,
        businessName: businessName.value,
        location: location.value,
        vatNumber: vatNumber.value,
        address: address.value,
        city: city.value,
        zipCode: zipCode.value,
        province: province.value,
        ownerName: ownerName.value,
      })
      emit('update:isDrawerOpen', false)
      nextTick(() => {
        refForm.value?.reset()
        refForm.value?.resetValidation()
      })
    }
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    temporary
    :width="400"
    location="end"
    class="scrollable-content"
    :model-value="props.isDrawerOpen"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Title -->
    <AppDrawerHeaderSection
      :title="tc('Add Structure')"
      @cancel="closeNavigationDrawer"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Form -->
          <VForm
            ref="refForm"
            v-model="isFormValid"
            @submit.prevent="onSubmit"
          >
            <VRow>
              <!-- 👉 ID -->
              <VCol cols="12">
                <VTextField
                  v-model="id"
                  :rules="[requiredValidator]"
                  :label="tc('ID')"
                  :placeholder="tc('Enter ID')"
                />
              </VCol>

              <!-- 👉 Business Name -->
              <VCol cols="12">
                <VTextField
                  v-model="businessName"
                  :rules="[requiredValidator]"
                  :label="tc('Business Name')"
                  :placeholder="tc('Enter Business Name')"
                />
              </VCol>

              <!-- 👉 Location -->
              <VCol cols="12">
                <VSelect
                  v-model="location"
                  :label="tc('Location')"
                  :placeholder="tc('Select Location')"
                  :rules="[requiredValidator]"
                  :items="[
                    tc('Location 1'),
                    tc('Location 2'),
                    tc('Location 3'),
                    tc('Location 4')
                  ]"
                />
              </VCol>

              <!-- 👉 VAT Number -->
              <VCol cols="12">
                <VTextField
                  v-model="vatNumber"
                  :rules="[requiredValidator]"
                  :label="tc('VAT Number')"
                  :placeholder="tc('Enter VAT Number')"
                />
              </VCol>

              <!-- 👉 Address -->
              <VCol cols="12">
                <VTextField
                  v-model="address"
                  :rules="[requiredValidator]"
                  :label="tc('Address')"
                  :placeholder="tc('Enter Address')"
                />
              </VCol>

              <!-- 👉 City -->
              <VCol cols="12">
                <VTextField
                  v-model="city"
                  :rules="[requiredValidator]"
                  :label="tc('City')"
                  :placeholder="tc('Enter City')"
                />
              </VCol>

              <!-- 👉 ZIP Code -->
              <VCol cols="12">
                <VTextField
                  v-model="zipCode"
                  :rules="[requiredValidator]"
                  :label="tc('ZIP Code')"
                  :placeholder="tc('Enter ZIP Code')"
                />
              </VCol>

              <!-- 👉 Province -->
              <VCol cols="12">
                <VTextField
                  v-model="province"
                  :rules="[requiredValidator]"
                  :label="tc('Province')"
                  :placeholder="tc('Enter Province')"
                />
              </VCol>

              <!-- 👉 Owner Name -->
              <VCol cols="12">
                <VTextField
                  v-model="ownerName"
                  :rules="[requiredValidator]"
                  :label="tc('Owner Name')"
                  :placeholder="tc('Enter Owner Name')"
                />
              </VCol>

              <!-- 👉 Submit and Cancel -->
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-4"
                >
                  {{ tc('Submit') }}
                </VBtn>
                <VBtn
                  type="reset"
                  variant="outlined"
                  color="error"
                  @click="closeNavigationDrawer"
                >
                  {{ tc('Cancel') }}
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style scoped>
.text-capitalize {
  text-transform: capitalize;
}
</style>
